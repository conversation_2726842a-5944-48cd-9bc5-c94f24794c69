package co.com.gedsys.base.application.usecase.planeacion.usuarios_externos;

import co.com.gedsys.base.application.common.EntityAlreadyExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.ExternalUserDto;
import co.com.gedsys.base.application.mapper.ExternalUsersAppMapper;
import co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales.CreateExternalUserCommand;
import co.com.gedsys.base.domain.usuario_externo.ExternalUser;
import co.com.gedsys.base.domain.usuario_externo.ExternalUsersRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@RequiredArgsConstructor
@Service
public class CreateExternalUserUseCase implements UseCase<CreateExternalUserCommand, ExternalUserDto> {
    private final ExternalUsersRepository externalUsersRepository;
    private final ExternalUsersAppMapper mapper;

    @Transactional
    @Override
    public ExternalUserDto execute(CreateExternalUserCommand input) {
        var externalUser = new ExternalUser(input.name(), input.identificationType(), input.identificationNumber());
        boolean externalUserAlreadyExists = externalUsersRepository.checkStock(externalUser);
        if (externalUserAlreadyExists) {
            throw new EntityAlreadyExistsException(String.format("El usuario con identificación %s ya existe",
                    externalUser.getIdentificationNumber()));
        }
        externalUser = mapper.toDomain(input, externalUser);
        var saved = externalUsersRepository.save(externalUser);

        return mapper.toDto(saved);
    }

}
