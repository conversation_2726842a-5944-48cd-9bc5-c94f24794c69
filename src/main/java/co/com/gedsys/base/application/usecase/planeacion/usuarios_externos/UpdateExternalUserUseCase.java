package co.com.gedsys.base.application.usecase.planeacion.usuarios_externos;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.ExternalUserDto;
import co.com.gedsys.base.application.mapper.ExternalUsersAppMapper;
import co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales.UpdateExternalUserCommand;
import co.com.gedsys.base.domain.usuario_externo.ExternalUsersRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class UpdateExternalUserUseCase implements UseCase<UpdateExternalUserCommand, ExternalUserDto> {
    private final ExternalUsersRepository externalUserRepository;
    private final ExternalUsersAppMapper mapper;

    @Transactional
    @Override
    public ExternalUserDto execute(UpdateExternalUserCommand input) {
        var externalUser = externalUserRepository.findById(input.id())
                .orElseThrow(() -> new EntityNotExistsException("No se encontró el usuario con id " + input.id()));
        externalUser = mapper.toDomain(input, externalUser);
        var updatedExternalUser = externalUserRepository.update(input.id(), externalUser);
        return mapper.toDto(updatedExternalUser);
    }
}
