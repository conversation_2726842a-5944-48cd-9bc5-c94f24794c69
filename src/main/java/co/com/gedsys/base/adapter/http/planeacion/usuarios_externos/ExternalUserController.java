package co.com.gedsys.base.adapter.http.planeacion.usuarios_externos;

import co.com.gedsys.base.application.usecase.planeacion.usuarios_externos.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;

@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/planeacion/usuarios-externos")
public class ExternalUserController implements ExternalUserAPIV1 {
    private final CreateExternalUserUseCase createUseCase;
    private final UpdateExternalUserUseCase updateUseCase;
    private final FindExternalUserUseCase findUseCase;
    private final DeleteExternalUserUseCase deleteUseCase;
    private final ListExternalUsersUseCase listUseCase;
    private final AddPropertiesExternalUserUseCase addPropertiesUseCase;

    private final ExternalUsersHttpMapper mapper;

    @Override
    public ExternalUserInfo create(ExternalUserCreateRequest request) {
        var command = mapper.toCommand(request);
        return mapper.toResponse(createUseCase.execute(command));
    }

    @Override
    public ExternalUserInfo addNewProperties(List<ExternalUserPropertyRegistration> request, UUID id) {
        var command = mapper.toCommand(id, request);
        var updated = addPropertiesUseCase.execute(command);
        return mapper.toResponse(updated);
    }

    @Override
    public ExternalUserInfo update(UUID id, ExternalUserUpdateRequest request) {
        var command = mapper.toCommand(id, request);
        var updated = updateUseCase.execute(command);
        return mapper.toResponse(updated);
    }

    @Override
    public ExternalUserInfo findById(UUID id) {
        return mapper.toResponse(findUseCase.execute(id));
    }

    @Override
    public List<ExternalUserItem> findAll() {
        return listUseCase.execute(null).stream().map(mapper::toItem).toList();
    }

    @Override
    public void delete(UUID id) {
        deleteUseCase.execute(id);
    }
}
