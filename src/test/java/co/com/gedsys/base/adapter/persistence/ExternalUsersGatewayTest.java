package co.com.gedsys.base.adapter.persistence;

import co.com.gedsys.base.domain.usuario_externo.ExternalUser;
import co.com.gedsys.base.domain.usuario_externo.ExternalUserIdentificationType;
import co.com.gedsys.base.domain.usuario_externo.ExternalUsersRepository;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

@DataJpaTest
@ActiveProfiles("test")
@Import({ExternalUsersGateway.class})
@DisplayName("Pruebas de persistencia para ExternalUsersGateway")
class ExternalUsersGatewayTest {

    @Autowired
    private ExternalUsersRepository repository;

    @Nested
    @DisplayName("con tipo NA")
    class ConTipoNA {

        @Test
        @DisplayName("debe guardar usuario tipo NA con número null exitosamente")
        void deberiaGuardarUsuarioTipoNAConNumeroNullExitosamente() {
            // Arrange
            var usuario = new ExternalUser("Usuario Sin ID", "NA", null);
            
            // Act & Assert
            assertDoesNotThrow(() -> {
                var saved = repository.save(usuario);
                assertNotNull(saved.getId());
                assertEquals("Usuario Sin ID", saved.getName());
                assertEquals(ExternalUserIdentificationType.NA, saved.getIdentificationType());
                assertNull(saved.getIdentificationNumber());
            });
        }

        @Test
        @DisplayName("debe encontrar usuario por nombre y tipo NA")
        void deberiaEncontrarUsuarioPorNombreYTipoNA() {
            // Arrange
            var usuario = new ExternalUser("Usuario Único", "NA", null);
            repository.save(usuario);
            
            // Act
            var encontrado = repository.findByNameAndIdentificationType("Usuario Único", ExternalUserIdentificationType.NA);
            
            // Assert
            assertTrue(encontrado.isPresent());
            assertEquals("Usuario Único", encontrado.get().getName());
            assertEquals(ExternalUserIdentificationType.NA, encontrado.get().getIdentificationType());
            assertNull(encontrado.get().getIdentificationNumber());
        }

        @Test
        @DisplayName("debe verificar existencia por nombre y tipo NA")
        void deberiaVerificarExistenciaPorNombreYTipoNA() {
            // Arrange
            var usuario = new ExternalUser("Usuario Existente", "NA", null);
            repository.save(usuario);
            
            // Act & Assert
            assertTrue(repository.existsByNameAndIdentificationType("Usuario Existente", ExternalUserIdentificationType.NA));
            assertFalse(repository.existsByNameAndIdentificationType("Usuario Inexistente", ExternalUserIdentificationType.NA));
        }

        @Test
        @DisplayName("debe permitir múltiples usuarios tipo NA con diferentes nombres")
        void deberiaPermitirMultiplesUsuariosTipoNAConDiferentesNombres() {
            // Arrange
            var usuario1 = new ExternalUser("Usuario NA 1", "NA", null);
            var usuario2 = new ExternalUser("Usuario NA 2", "NA", null);
            
            // Act & Assert
            assertDoesNotThrow(() -> {
                repository.save(usuario1);
                repository.save(usuario2);
            });
            
            assertTrue(repository.existsByNameAndIdentificationType("Usuario NA 1", ExternalUserIdentificationType.NA));
            assertTrue(repository.existsByNameAndIdentificationType("Usuario NA 2", ExternalUserIdentificationType.NA));
        }
    }

    @Nested
    @DisplayName("con otros tipos de identificación")
    class ConOtrosTiposDeIdentificacion {

        @Test
        @DisplayName("debe guardar usuario tipo CC con número válido")
        void deberiaGuardarUsuarioTipoCCConNumeroValido() {
            // Arrange
            var usuario = new ExternalUser("Usuario CC", "CC", "12345678");
            
            // Act & Assert
            assertDoesNotThrow(() -> {
                var saved = repository.save(usuario);
                assertNotNull(saved.getId());
                assertEquals("Usuario CC", saved.getName());
                assertEquals(ExternalUserIdentificationType.CC, saved.getIdentificationType());
                assertEquals("12345678", saved.getIdentificationNumber());
            });
        }

        @Test
        @DisplayName("debe encontrar usuario por nombre y tipo CC")
        void deberiaEncontrarUsuarioPorNombreYTipoCC() {
            // Arrange
            var usuario = new ExternalUser("Usuario CC Único", "CC", "87654321");
            repository.save(usuario);
            
            // Act
            var encontrado = repository.findByNameAndIdentificationType("Usuario CC Único", ExternalUserIdentificationType.CC);
            
            // Assert
            assertTrue(encontrado.isPresent());
            assertEquals("Usuario CC Único", encontrado.get().getName());
            assertEquals(ExternalUserIdentificationType.CC, encontrado.get().getIdentificationType());
            assertEquals("87654321", encontrado.get().getIdentificationNumber());
        }

        @Test
        @DisplayName("debe verificar existencia por nombre y tipo CC")
        void deberiaVerificarExistenciaPorNombreYTipoCC() {
            // Arrange
            var usuario = new ExternalUser("Usuario CC Existente", "CC", "11223344");
            repository.save(usuario);
            
            // Act & Assert
            assertTrue(repository.existsByNameAndIdentificationType("Usuario CC Existente", ExternalUserIdentificationType.CC));
            assertFalse(repository.existsByNameAndIdentificationType("Usuario CC Inexistente", ExternalUserIdentificationType.CC));
        }
    }

    @Nested
    @DisplayName("validaciones cruzadas")
    class ValidacionesCruzadas {

        @Test
        @DisplayName("debe distinguir entre usuarios con mismo nombre pero diferentes tipos")
        void deberiaDistinguirEntreUsuariosConMismoNombrePeroDiferentesTipos() {
            // Arrange
            var usuarioNA = new ExternalUser("Juan Pérez", "NA", null);
            var usuarioCC = new ExternalUser("Juan Pérez", "CC", "12345678");
            
            // Act
            repository.save(usuarioNA);
            repository.save(usuarioCC);
            
            // Assert
            assertTrue(repository.existsByNameAndIdentificationType("Juan Pérez", ExternalUserIdentificationType.NA));
            assertTrue(repository.existsByNameAndIdentificationType("Juan Pérez", ExternalUserIdentificationType.CC));
            
            var encontradoNA = repository.findByNameAndIdentificationType("Juan Pérez", ExternalUserIdentificationType.NA);
            var encontradoCC = repository.findByNameAndIdentificationType("Juan Pérez", ExternalUserIdentificationType.CC);
            
            assertTrue(encontradoNA.isPresent());
            assertTrue(encontradoCC.isPresent());
            assertNull(encontradoNA.get().getIdentificationNumber());
            assertEquals("12345678", encontradoCC.get().getIdentificationNumber());
        }

        @Test
        @DisplayName("no debe encontrar usuario con combinación inexistente de nombre y tipo")
        void noDeberiaEncontrarUsuarioConCombinacionInexistenteDeNombreYTipo() {
            // Arrange
            var usuario = new ExternalUser("Usuario Test", "CC", "12345678");
            repository.save(usuario);
            
            // Act & Assert
            assertFalse(repository.existsByNameAndIdentificationType("Usuario Test", ExternalUserIdentificationType.NA));
            assertTrue(repository.findByNameAndIdentificationType("Usuario Test", ExternalUserIdentificationType.NA).isEmpty());
        }
    }
}
