package co.com.gedsys.base.adapter.http.planeacion.usuarios_externos;

import co.com.gedsys.base.application.usecase.planeacion.usuarios_externos.*;
import co.com.gedsys.base.domain.usuario_externo.ExternalUserIdentificationType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("Pruebas para ExternalUserController")
class ExternalUserControllerTest {

    @Mock
    private CreateExternalUserUseCase createUseCase;
    
    @Mock
    private UpdateExternalUserUseCase updateUseCase;
    
    @Mock
    private FindExternalUserUseCase findUseCase;
    
    @Mock
    private DeleteExternalUserUseCase deleteUseCase;
    
    @Mock
    private ListExternalUsersUseCase listUseCase;
    
    @Mock
    private AddPropertiesExternalUserUseCase addPropertiesUseCase;
    
    @Mock
    private ExternalUsersHttpMapper mapper;
    
    private ExternalUserController controller;

    @BeforeEach
    void setUp() {
        controller = new ExternalUserController(
            createUseCase, updateUseCase, findUseCase, 
            deleteUseCase, listUseCase, addPropertiesUseCase, mapper
        );
    }

    @Nested
    @DisplayName("al crear usuario tipo NA")
    class AlCrearUsuarioTipoNA {

        @Test
        @DisplayName("debe crear usuario tipo NA exitosamente")
        void deberiaCrearUsuarioTipoNAExitosamente() {
            // Arrange
            var request = new ExternalUserCreateRequest(
                "Usuario Sin ID",
                "Saludo",
                ExternalUserIdentificationType.NA,
                null, // Número null para tipo NA
                "Notas",
                new ArrayList<>()
            );
            
            var expectedDto = new ExternalUserDto();
            var expectedResponse = new ExternalUserInfo();
            
            when(mapper.toCommand(request)).thenReturn(null); // Command no es relevante para esta prueba
            when(createUseCase.execute(any())).thenReturn(expectedDto);
            when(mapper.toResponse(expectedDto)).thenReturn(expectedResponse);
            
            // Act & Assert
            assertDoesNotThrow(() -> {
                var result = controller.create(request);
                assertSame(expectedResponse, result);
            });
            
            verify(createUseCase).execute(any());
        }

        @Test
        @DisplayName("debe lanzar excepción al crear usuario tipo NA con número")
        void deberiaLanzarExcepcionAlCrearUsuarioTipoNAConNumero() {
            // Arrange
            var request = new ExternalUserCreateRequest(
                "Usuario Inválido",
                "Saludo",
                ExternalUserIdentificationType.NA,
                "12345", // ❌ Número no permitido para tipo NA
                "Notas",
                new ArrayList<>()
            );
            
            // Act & Assert
            var exception = assertThrows(IllegalArgumentException.class, 
                () -> controller.create(request));
            assertTrue(exception.getMessage().contains("Tipo NA no debe incluir número de identificación"));
            
            // Verify que no se llamó al use case
            verify(createUseCase, never()).execute(any());
        }
    }

    @Nested
    @DisplayName("al crear usuario con otros tipos")
    class AlCrearUsuarioConOtrosTipos {

        @Test
        @DisplayName("debe crear usuario tipo CC exitosamente")
        void deberiaCrearUsuarioTipoCCExitosamente() {
            // Arrange
            var request = new ExternalUserCreateRequest(
                "Usuario CC",
                "Saludo",
                ExternalUserIdentificationType.CC,
                "12345678", // Número requerido para tipo CC
                "Notas",
                new ArrayList<>()
            );
            
            var expectedDto = new ExternalUserDto();
            var expectedResponse = new ExternalUserInfo();
            
            when(mapper.toCommand(request)).thenReturn(null);
            when(createUseCase.execute(any())).thenReturn(expectedDto);
            when(mapper.toResponse(expectedDto)).thenReturn(expectedResponse);
            
            // Act & Assert
            assertDoesNotThrow(() -> {
                var result = controller.create(request);
                assertSame(expectedResponse, result);
            });
            
            verify(createUseCase).execute(any());
        }

        @Test
        @DisplayName("debe lanzar excepción al crear usuario tipo CC sin número")
        void deberiaLanzarExcepcionAlCrearUsuarioTipoCCSinNumero() {
            // Arrange
            var request = new ExternalUserCreateRequest(
                "Usuario Inválido",
                "Saludo",
                ExternalUserIdentificationType.CC,
                null, // ❌ Número requerido para tipo CC
                "Notas",
                new ArrayList<>()
            );
            
            // Act & Assert
            var exception = assertThrows(IllegalArgumentException.class, 
                () -> controller.create(request));
            assertTrue(exception.getMessage().contains("Tipo CC requiere número de identificación"));
            
            verify(createUseCase, never()).execute(any());
        }

        @Test
        @DisplayName("debe lanzar excepción al crear usuario tipo CC con número blank")
        void deberiaLanzarExcepcionAlCrearUsuarioTipoCCConNumeroBlank() {
            // Arrange
            var request = new ExternalUserCreateRequest(
                "Usuario Inválido",
                "Saludo",
                ExternalUserIdentificationType.CC,
                "   ", // ❌ Número blank no permitido
                "Notas",
                new ArrayList<>()
            );
            
            // Act & Assert
            var exception = assertThrows(IllegalArgumentException.class, 
                () -> controller.create(request));
            assertTrue(exception.getMessage().contains("Tipo CC requiere número de identificación"));
            
            verify(createUseCase, never()).execute(any());
        }
    }

    @Nested
    @DisplayName("al actualizar usuario")
    class AlActualizarUsuario {

        private UUID userId;

        @BeforeEach
        void setUp() {
            userId = UUID.randomUUID();
        }

        @Test
        @DisplayName("debe actualizar usuario a tipo NA exitosamente")
        void deberiaActualizarUsuarioATipoNAExitosamente() {
            // Arrange
            var request = new ExternalUserUpdateRequest(
                "Usuario Actualizado",
                "Saludo",
                ExternalUserIdentificationType.NA,
                null, // Número null para tipo NA
                "Notas",
                "ACTIVO",
                new ArrayList<>()
            );
            
            var expectedDto = new ExternalUserDto();
            var expectedResponse = new ExternalUserInfo();
            
            when(mapper.toCommand(userId, request)).thenReturn(null);
            when(updateUseCase.execute(any())).thenReturn(expectedDto);
            when(mapper.toResponse(expectedDto)).thenReturn(expectedResponse);
            
            // Act & Assert
            assertDoesNotThrow(() -> {
                var result = controller.update(userId, request);
                assertSame(expectedResponse, result);
            });
            
            verify(updateUseCase).execute(any());
        }

        @Test
        @DisplayName("debe lanzar excepción al actualizar a tipo NA con número")
        void deberiaLanzarExcepcionAlActualizarATipoNAConNumero() {
            // Arrange
            var request = new ExternalUserUpdateRequest(
                "Usuario Inválido",
                "Saludo",
                ExternalUserIdentificationType.NA,
                "12345", // ❌ Número no permitido para tipo NA
                "Notas",
                "ACTIVO",
                new ArrayList<>()
            );
            
            // Act & Assert
            var exception = assertThrows(IllegalArgumentException.class, 
                () -> controller.update(userId, request));
            assertTrue(exception.getMessage().contains("Tipo NA no debe incluir número de identificación"));
            
            verify(updateUseCase, never()).execute(any());
        }

        @Test
        @DisplayName("debe actualizar usuario tipo CC exitosamente")
        void deberiaActualizarUsuarioTipoCCExitosamente() {
            // Arrange
            var request = new ExternalUserUpdateRequest(
                "Usuario CC Actualizado",
                "Saludo",
                ExternalUserIdentificationType.CC,
                "87654321", // Número requerido para tipo CC
                "Notas",
                "ACTIVO",
                new ArrayList<>()
            );
            
            var expectedDto = new ExternalUserDto();
            var expectedResponse = new ExternalUserInfo();
            
            when(mapper.toCommand(userId, request)).thenReturn(null);
            when(updateUseCase.execute(any())).thenReturn(expectedDto);
            when(mapper.toResponse(expectedDto)).thenReturn(expectedResponse);
            
            // Act & Assert
            assertDoesNotThrow(() -> {
                var result = controller.update(userId, request);
                assertSame(expectedResponse, result);
            });
            
            verify(updateUseCase).execute(any());
        }
    }

    @Nested
    @DisplayName("validaciones de tipo de identificación")
    class ValidacionesTipoIdentificacion {

        @Test
        @DisplayName("debe validar todos los tipos de identificación válidos")
        void deberiaValidarTodosLosTiposDeIdentificacionValidos() {
            // Arrange & Act & Assert
            for (ExternalUserIdentificationType tipo : ExternalUserIdentificationType.values()) {
                if (tipo == ExternalUserIdentificationType.NA) {
                    // Para NA, número debe ser null
                    var request = new ExternalUserCreateRequest(
                        "Usuario " + tipo,
                        "Saludo",
                        tipo,
                        null,
                        "Notas",
                        new ArrayList<>()
                    );
                    
                    when(mapper.toCommand(any())).thenReturn(null);
                    when(createUseCase.execute(any())).thenReturn(new ExternalUserDto());
                    when(mapper.toResponse(any())).thenReturn(new ExternalUserInfo());
                    
                    assertDoesNotThrow(() -> controller.create(request), 
                        "Tipo " + tipo + " debería ser válido con número null");
                } else {
                    // Para otros tipos, número debe estar presente
                    var request = new ExternalUserCreateRequest(
                        "Usuario " + tipo,
                        "Saludo",
                        tipo,
                        "12345678",
                        "Notas",
                        new ArrayList<>()
                    );
                    
                    when(mapper.toCommand(any())).thenReturn(null);
                    when(createUseCase.execute(any())).thenReturn(new ExternalUserDto());
                    when(mapper.toResponse(any())).thenReturn(new ExternalUserInfo());
                    
                    assertDoesNotThrow(() -> controller.create(request), 
                        "Tipo " + tipo + " debería ser válido con número presente");
                }
            }
        }
    }
}
